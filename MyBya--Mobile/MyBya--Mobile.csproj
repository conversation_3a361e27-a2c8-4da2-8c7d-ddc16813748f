<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net8.0-android;net8.0-ios</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net8.0-windows10.0.19041.0</TargetFrameworks>
    <OutputType>Exe</OutputType>
    <RootNamespace>MyBya</RootNamespace>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationTitle>MyBya</ApplicationTitle>
    <ApplicationId>com.mybya.mybyamobile</ApplicationId>
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-ios|AnyCPU'">
    <CreatePackage>false</CreatePackage>
    <CodesignEntitlements>Platforms/iOS/Entitlements.plist</CodesignEntitlements>
    <CodesignProvision>MyByaDevelopment</CodesignProvision>
    <CodesignKey>Apple Development: Gabriel Juren (7L5NVMPH6Y)</CodesignKey>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-android|AnyCPU'">
    <AndroidPackageFormat>apk</AndroidPackageFormat>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <CodesignKey>Apple Distribution: MyBya, LLC (5W57G8UTTV)</CodesignKey>
    <CodesignProvision>MyBya_ProvisioningProfile</CodesignProvision>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-ios|AnyCPU'">
    <CodesignEntitlements>Platforms/iOS/Entitlements.plist</CodesignEntitlements>
    <CreatePackage>false</CreatePackage>
  </PropertyGroup>
  <ItemGroup>
    <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />
    <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />
    <MauiImage Include="Resources\Images\*" />
    <!-- <MauiImage Include="Resources\Images\*.png" BaseSize="16,16" />
    <MauiImage Include="Resources\Images\*.jpg" BaseSize="16,16" />
    <MauiImage Include="Resources\Images\*.jpeg" BaseSize="16,16" /> -->
    <MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />
    <MauiImage Update="Resources\Images\arrow_left.png" Resize="False" BaseSize="40,40" />
    <MauiFont Include="Resources\Fonts\*" />
    <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Platforms/iOS/Entitlements.plist" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Maui.MediaElement" Version="4.1.2" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="$(MauiVersion)" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="CommunityToolkit.Maui" Version="9.0.2" />
    <PackageReference Include="CommunityToolkit.Maui.Core" Version="9.0.2" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="sqlite-net-pcl" Version="1.9.172" />
    <PackageReference Include="Syncfusion.Maui.Toolkit" Version="1.0.5" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
    <PackageReference Include="JWT" Version="11.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.13.0" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Platforms/iOS/Certificates/" />
    <Folder Include="Resources/Members/" />
    <Folder Include="Services/Entities/" />
    <Folder Include="Services\TestResultPaceChart\" />
    <Folder Include="Models\TestResultPaceChart\" />
    <Folder Include="Services\Entities\TestResultPaceChart\" />
    <Folder Include="Services\TestResultAnrc\" />
    <Folder Include="Models\TestResultAnrc\" />
    <Folder Include="Services\Entities\TestResultAnrc\" />
    <Folder Include="DataAccess\Common\" />
    <Folder Include="Services\TrainingPlanDetail\" />
    <Folder Include="Models\TrainingPlanDetail\" />
    <Folder Include="Services\Entities\TrainingPlanDetail\" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Configuration\appsettingsDev.json" />
    <None Remove="Services\TestResultPaceChart\" />
    <None Remove="Models\TestResultPaceChart\" />
    <None Remove="Services\Entities\TestResultPaceChart\" />
    <None Remove="Services\TestResultAnrc\" />
    <None Remove="Models\TestResultAnrc\" />
    <None Remove="Services\Entities\TestResultAnrc\" />
    <None Remove="DataAccess\Common\" />
    <None Remove="Services\TrainingPlanDetail\" />
    <None Remove="Models\TrainingPlanDetail\" />
    <None Remove="Services\Entities\TrainingPlanDetail\" />
    <None Remove="Resources\Images\arrow_back.svg" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Configuration\appsettingsDev.json" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Ui/Pages/TestProtocol/TestProtocolInitialCreationPage.xaml.cs">
      <DependentUpon>TestProtocolInitialCreationPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Ui/Views/BaseView.xaml.cs">
      <DependentUpon>BaseView.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
</Project>
